/* Custom styles for QuickB<PERSON>s Account Manager */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 700;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0 !important;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-primary {
    background-color: #0066cc;
    border-color: #0066cc;
}

.btn-primary:hover {
    background-color: #0052a3;
    border-color: #0052a3;
}

.badge {
    font-size: 0.75em;
    font-weight: 500;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 102, 204, 0.05);
}

.form-control:focus {
    border-color: #0066cc;
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.form-select:focus {
    border-color: #0066cc;
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.alert {
    border: none;
    border-radius: 8px;
}

.display-4 {
    font-weight: 700;
}

.lead {
    font-size: 1.1rem;
    color: #6c757d;
}

.text-primary {
    color: #0066cc !important;
}

.bg-primary {
    background-color: #0066cc !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}

.navbar-text .badge {
    font-size: 0.7rem;
}

footer {
    margin-top: auto;
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Status indicators */
.status-connected {
    color: #28a745;
}

.status-disconnected {
    color: #dc3545;
}

/* Account type colors */
.account-asset {
    border-left: 4px solid #28a745;
}

.account-liability {
    border-left: 4px solid #dc3545;
}

.account-equity {
    border-left: 4px solid #6f42c1;
}

.account-income {
    border-left: 4px solid #007bff;
}

.account-expense {
    border-left: 4px solid #fd7e14;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom form styling */
.form-floating > label {
    color: #6c757d;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: #0066cc;
}

/* Search input styling */
.input-group .form-control:focus {
    z-index: 3;
}

/* Table improvements */
.table tbody tr {
    transition: background-color 0.15s ease-in-out;
}

.table-borderless th,
.table-borderless td {
    border: none;
}

/* Button improvements */
.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Card improvements */
.card-title {
    color: #495057;
    font-weight: 600;
}

.card-text {
    color: #6c757d;
}

/* Footer styling */
footer {
    border-top: 1px solid #dee2e6;
}

/* Utility classes */
.text-muted-light {
    color: #adb5bd !important;
}

.bg-light-custom {
    background-color: #f8f9fa !important;
}

.border-light-custom {
    border-color: #e9ecef !important;
}
