import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # QuickBooks OAuth credentials
    QB_CLIENT_ID = "ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd"
    QB_CLIENT_SECRET = "0HyzRWUl1pa8hnS5GcpuCWs0M0OpcLDrmw9hQX4u"
    QB_ENVIRONMENT = "sandbox"  # Use 'production' for live environment
    QB_REDIRECT_URI = "http://localhost:5000/callback"
    
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # QuickBooks API URLs
    QB_DISCOVERY_DOCUMENT_URL = "https://appcenter.intuit.com/api/v1/OpenID_sandbox"
    QB_BASE_URL_SANDBOX = "https://sandbox-quickbooks.api.intuit.com"
    QB_BASE_URL_PRODUCTION = "https://quickbooks.api.intuit.com"
    
    @property
    def QB_BASE_URL(self):
        return self.QB_BASE_URL_SANDBOX if self.QB_ENVIRONMENT == "sandbox" else self.QB_BASE_URL_PRODUCTION
