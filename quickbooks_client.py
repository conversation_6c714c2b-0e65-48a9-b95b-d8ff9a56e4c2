from intuitlib.client import AuthClient
from intuitlib.enums import Scopes
from quickbooks import QuickBooks
from quickbooks.objects.account import Account
from quickbooks.exceptions import QuickbooksException
from config import Config
import logging
import json
import requests
from datetime import datetime

# Configure detailed logging with UTF-8 encoding
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('quickbooks_api.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class DetailedLogger:
    """Custom logger for detailed API request/response logging"""

    @staticmethod
    def log_request(method, url, headers=None, data=None, params=None):
        """Log detailed request information"""
        logger.info("=" * 80)
        logger.info(f">>> OUTGOING REQUEST - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 80)
        logger.info(f"METHOD: {method}")
        logger.info(f"URL: {url}")

        if params:
            logger.info(f"QUERY PARAMETERS:")
            for key, value in params.items():
                logger.info(f"  {key}: {value}")

        if headers:
            logger.info(f"HEADERS:")
            for key, value in headers.items():
                # Mask sensitive headers
                if key.lower() in ['authorization', 'client_secret', 'access_token']:
                    logger.info(f"  {key}: {'*' * 20}")
                else:
                    logger.info(f"  {key}: {value}")

        if data:
            logger.info(f"REQUEST BODY:")
            if isinstance(data, dict):
                logger.info(json.dumps(data, indent=2))
            else:
                logger.info(str(data))

        logger.info("=" * 80)

    @staticmethod
    def log_response(response, request_start_time=None):
        """Log detailed response information"""
        duration = ""
        if request_start_time:
            duration = f" (Duration: {datetime.now() - request_start_time})"

        logger.info("=" * 80)
        logger.info(f"<<< INCOMING RESPONSE{duration} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 80)
        logger.info(f"STATUS CODE: {response.status_code}")
        logger.info(f"STATUS TEXT: {response.reason}")

        logger.info(f"RESPONSE HEADERS:")
        for key, value in response.headers.items():
            logger.info(f"  {key}: {value}")

        try:
            if response.content:
                content_type = response.headers.get('content-type', '').lower()
                if 'application/json' in content_type:
                    json_data = response.json()
                    logger.info(f"RESPONSE BODY (JSON):")
                    logger.info(json.dumps(json_data, indent=2))
                else:
                    logger.info(f"RESPONSE BODY (TEXT):")
                    logger.info(response.text[:1000] + "..." if len(response.text) > 1000 else response.text)
            else:
                logger.info("RESPONSE BODY: (empty)")
        except Exception as e:
            logger.error(f"Error parsing response body: {str(e)}")
            logger.info(f"RAW RESPONSE: {response.text[:500]}...")

        logger.info("=" * 80)

    @staticmethod
    def log_oauth_flow(step, details):
        """Log OAuth flow steps"""
        logger.info("[OAUTH] " + "=" * 70)
        logger.info(f"OAUTH STEP: {step}")
        logger.info("[OAUTH] " + "=" * 70)
        for key, value in details.items():
            if key.lower() in ['client_secret', 'access_token', 'refresh_token']:
                logger.info(f"{key}: {'*' * 20}")
            else:
                logger.info(f"{key}: {value}")
        logger.info("[OAUTH] " + "=" * 70)

class QuickBooksClient:
    def __init__(self):
        self.config = Config()
        self.auth_client = None
        self.qb_client = None
        self.company_id = None
        self.refresh_token = None
        
    def setup_auth_client(self, access_token=None):
        """Setup the authentication client"""
        self.auth_client = AuthClient(
            client_id=self.config.QB_CLIENT_ID,
            client_secret=self.config.QB_CLIENT_SECRET,
            access_token=access_token,
            environment=self.config.QB_ENVIRONMENT,
            redirect_uri=self.config.QB_REDIRECT_URI,
        )
        return self.auth_client
    
    def get_authorization_url(self):
        """Get the authorization URL for OAuth flow"""
        if not self.auth_client:
            self.setup_auth_client()

        # Use proper Scopes enum
        scopes = [Scopes.ACCOUNTING]

        DetailedLogger.log_oauth_flow("GET_AUTHORIZATION_URL", {
            "client_id": self.config.QB_CLIENT_ID,
            "redirect_uri": self.config.QB_REDIRECT_URI,
            "scopes": [scope.value for scope in scopes],
            "environment": self.config.QB_ENVIRONMENT
        })

        try:
            auth_url = self.auth_client.get_authorization_url(scopes)

            DetailedLogger.log_oauth_flow("AUTHORIZATION_URL_GENERATED", {
                "auth_url": auth_url,
                "url_length": len(auth_url)
            })

            return auth_url
        except Exception as e:
            logger.error(f"Error generating authorization URL: {str(e)}")
            DetailedLogger.log_oauth_flow("AUTHORIZATION_URL_ERROR", {
                "error": str(e),
                "error_type": type(e).__name__
            })
            raise
    
    def get_bearer_token(self, auth_code, realm_id):
        """Exchange authorization code for access token"""
        DetailedLogger.log_oauth_flow("EXCHANGE_AUTH_CODE", {
            "auth_code": auth_code[:10] + "..." if len(auth_code) > 10 else auth_code,
            "realm_id": realm_id,
            "client_id": self.config.QB_CLIENT_ID
        })

        try:
            # Log the token exchange request
            request_start = datetime.now()

            self.auth_client.get_bearer_token(auth_code, realm_id=realm_id)

            self.company_id = realm_id
            self.refresh_token = self.auth_client.refresh_token

            DetailedLogger.log_oauth_flow("TOKEN_EXCHANGE_SUCCESS", {
                "company_id": self.company_id,
                "access_token": "***MASKED***",
                "refresh_token": "***MASKED***",
                "token_type": getattr(self.auth_client, 'token_type', 'Bearer'),
                "expires_in": getattr(self.auth_client, 'expires_in', 'Unknown'),
                "duration": str(datetime.now() - request_start)
            })

            # Setup QuickBooks client
            self.qb_client = QuickBooks(
                auth_client=self.auth_client,
                refresh_token=self.refresh_token,
                company_id=self.company_id,
            )

            logger.info("QuickBooks client initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Error getting bearer token: {str(e)}")
            DetailedLogger.log_oauth_flow("TOKEN_EXCHANGE_ERROR", {
                "error": str(e),
                "error_type": type(e).__name__,
                "auth_code_length": len(auth_code),
                "realm_id": realm_id
            })
            return False
    
    def setup_client_with_tokens(self, access_token, refresh_token, company_id):
        """Setup client with existing tokens"""
        self.setup_auth_client(access_token)
        self.refresh_token = refresh_token
        self.company_id = company_id
        
        self.qb_client = QuickBooks(
            auth_client=self.auth_client,
            refresh_token=self.refresh_token,
            company_id=self.company_id,
        )
    
    def create_account(self, account_data):
        """Create a new account in QuickBooks"""
        logger.info("[ACCOUNT] " + "=" * 68)
        logger.info("CREATING QUICKBOOKS ACCOUNT")
        logger.info("[ACCOUNT] " + "=" * 68)
        logger.info(f"Account Data: {json.dumps(account_data, indent=2)}")

        try:
            if not self.qb_client:
                raise Exception("QuickBooks client not initialized")

            # Create account object
            account = Account()
            account.Name = account_data.get('name')
            account.AccountType = account_data.get('account_type')
            account.AccountSubType = account_data.get('account_sub_type')

            if account_data.get('description'):
                account.Description = account_data.get('description')

            if account_data.get('account_number'):
                account.AcctNum = account_data.get('account_number')

            logger.info(f"Account object created:")
            logger.info(f"  Name: {account.Name}")
            logger.info(f"  Type: {account.AccountType}")
            logger.info(f"  SubType: {account.AccountSubType}")
            logger.info(f"  Description: {getattr(account, 'Description', 'None')}")
            logger.info(f"  Account Number: {getattr(account, 'AcctNum', 'None')}")

            # Log the API request details
            request_start = datetime.now()

            # Save the account (this will make the API call)
            account.save(qb=self.qb_client)

            duration = datetime.now() - request_start

            logger.info("[ACCOUNT] " + "=" * 68)
            logger.info("ACCOUNT CREATION SUCCESS")
            logger.info("[ACCOUNT] " + "=" * 68)
            logger.info(f"Account created successfully:")
            logger.info(f"  ID: {account.Id}")
            logger.info(f"  Name: {account.Name}")
            logger.info(f"  Type: {account.AccountType}")
            logger.info(f"  Creation Duration: {duration}")
            logger.info("[ACCOUNT] " + "=" * 68)

            return account

        except QuickbooksException as e:
            logger.error("[ACCOUNT] " + "=" * 68)
            logger.error("QUICKBOOKS API ERROR - ACCOUNT CREATION")
            logger.error("[ACCOUNT] " + "=" * 68)
            logger.error(f"Error Code: {getattr(e, 'error_code', 'Unknown')}")
            logger.error(f"Error Message: {e.message}")
            logger.error(f"Error Detail: {getattr(e, 'detail', 'No additional details')}")
            logger.error("[ACCOUNT] " + "=" * 68)
            raise Exception(f"QuickBooks error: {e.message}")
        except Exception as e:
            logger.error("[ACCOUNT] " + "=" * 68)
            logger.error("GENERAL ERROR - ACCOUNT CREATION")
            logger.error("[ACCOUNT] " + "=" * 68)
            logger.error(f"Error Type: {type(e).__name__}")
            logger.error(f"Error Message: {str(e)}")
            logger.error("[ACCOUNT] " + "=" * 68)
            raise
    
    def get_accounts(self, active_only=True):
        """Get all accounts from QuickBooks"""
        logger.info("[ACCOUNTS] " + "=" * 66)
        logger.info("RETRIEVING QUICKBOOKS ACCOUNTS")
        logger.info("[ACCOUNTS] " + "=" * 66)
        logger.info(f"Filter: {'Active accounts only' if active_only else 'All accounts'}")

        try:
            if not self.qb_client:
                raise Exception("QuickBooks client not initialized")

            request_start = datetime.now()

            if active_only:
                logger.info("Making API call: Account.filter(Active=True)")
                accounts = Account.filter(Active=True, qb=self.qb_client)
            else:
                logger.info("Making API call: Account.all()")
                accounts = Account.all(qb=self.qb_client)

            duration = datetime.now() - request_start

            logger.info("[ACCOUNTS] " + "=" * 66)
            logger.info("ACCOUNTS RETRIEVAL SUCCESS")
            logger.info("[ACCOUNTS] " + "=" * 66)
            logger.info(f"Total accounts retrieved: {len(accounts)}")
            logger.info(f"Retrieval duration: {duration}")

            # Log account summary
            if accounts:
                account_types = {}
                for account in accounts:
                    acc_type = account.AccountType
                    account_types[acc_type] = account_types.get(acc_type, 0) + 1

                logger.info("Account breakdown by type:")
                for acc_type, count in account_types.items():
                    logger.info(f"  {acc_type}: {count}")

                logger.info("Sample accounts:")
                for i, account in enumerate(accounts[:5]):  # Show first 5
                    logger.info(f"  {i+1}. {account.Name} ({account.AccountType}) - ID: {account.Id}")
                if len(accounts) > 5:
                    logger.info(f"  ... and {len(accounts) - 5} more accounts")

            logger.info("[ACCOUNTS] " + "=" * 66)
            return accounts

        except QuickbooksException as e:
            logger.error("[ACCOUNTS] " + "=" * 66)
            logger.error("QUICKBOOKS API ERROR - GET ACCOUNTS")
            logger.error("[ACCOUNTS] " + "=" * 66)
            logger.error(f"Error Code: {getattr(e, 'error_code', 'Unknown')}")
            logger.error(f"Error Message: {e.message}")
            logger.error(f"Error Detail: {getattr(e, 'detail', 'No additional details')}")
            logger.error("[ACCOUNTS] " + "=" * 66)
            raise Exception(f"QuickBooks error: {e.message}")
        except Exception as e:
            logger.error("[ACCOUNTS] " + "=" * 66)
            logger.error("GENERAL ERROR - GET ACCOUNTS")
            logger.error("[ACCOUNTS] " + "=" * 66)
            logger.error(f"Error Type: {type(e).__name__}")
            logger.error(f"Error Message: {str(e)}")
            logger.error("[ACCOUNTS] " + "=" * 66)
            raise
    
    def get_account_by_id(self, account_id):
        """Get a specific account by ID"""
        try:
            if not self.qb_client:
                raise Exception("QuickBooks client not initialized")
            
            account = Account.get(account_id, qb=self.qb_client)
            logger.info(f"Retrieved account: {account.Name} (ID: {account.Id})")
            return account
            
        except QuickbooksException as e:
            logger.error(f"QuickBooks error getting account: {e.message}")
            raise Exception(f"QuickBooks error: {e.message}")
        except Exception as e:
            logger.error(f"Error getting account: {str(e)}")
            raise
