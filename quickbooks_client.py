from intuitlib.client import AuthClient
from quickbooks import QuickBooks
from quickbooks.objects.account import Account
from quickbooks.exceptions import QuickbooksException
from config import Config
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuickBooksClient:
    def __init__(self):
        self.config = Config()
        self.auth_client = None
        self.qb_client = None
        self.company_id = None
        self.refresh_token = None
        
    def setup_auth_client(self, access_token=None):
        """Setup the authentication client"""
        self.auth_client = AuthClient(
            client_id=self.config.QB_CLIENT_ID,
            client_secret=self.config.QB_CLIENT_SECRET,
            access_token=access_token,
            environment=self.config.QB_ENVIRONMENT,
            redirect_uri=self.config.QB_REDIRECT_URI,
        )
        return self.auth_client
    
    def get_authorization_url(self):
        """Get the authorization URL for OAuth flow"""
        if not self.auth_client:
            self.setup_auth_client()
        
        scopes = [
            'com.intuit.quickbooks.accounting'
        ]
        
        auth_url = self.auth_client.get_authorization_url(scopes)
        return auth_url
    
    def get_bearer_token(self, auth_code, realm_id):
        """Exchange authorization code for access token"""
        try:
            self.auth_client.get_bearer_token(auth_code, realm_id=realm_id)
            self.company_id = realm_id
            self.refresh_token = self.auth_client.refresh_token
            
            # Setup QuickBooks client
            self.qb_client = QuickBooks(
                auth_client=self.auth_client,
                refresh_token=self.refresh_token,
                company_id=self.company_id,
            )
            
            return True
        except Exception as e:
            logger.error(f"Error getting bearer token: {str(e)}")
            return False
    
    def setup_client_with_tokens(self, access_token, refresh_token, company_id):
        """Setup client with existing tokens"""
        self.setup_auth_client(access_token)
        self.refresh_token = refresh_token
        self.company_id = company_id
        
        self.qb_client = QuickBooks(
            auth_client=self.auth_client,
            refresh_token=self.refresh_token,
            company_id=self.company_id,
        )
    
    def create_account(self, account_data):
        """Create a new account in QuickBooks"""
        try:
            if not self.qb_client:
                raise Exception("QuickBooks client not initialized")
            
            account = Account()
            account.Name = account_data.get('name')
            account.AccountType = account_data.get('account_type')
            account.AccountSubType = account_data.get('account_sub_type')
            
            if account_data.get('description'):
                account.Description = account_data.get('description')
            
            if account_data.get('account_number'):
                account.AcctNum = account_data.get('account_number')
            
            # Save the account
            account.save(qb=self.qb_client)
            
            logger.info(f"Account created successfully: {account.Name} (ID: {account.Id})")
            return account
            
        except QuickbooksException as e:
            logger.error(f"QuickBooks error creating account: {e.message}")
            raise Exception(f"QuickBooks error: {e.message}")
        except Exception as e:
            logger.error(f"Error creating account: {str(e)}")
            raise
    
    def get_accounts(self, active_only=True):
        """Get all accounts from QuickBooks"""
        try:
            if not self.qb_client:
                raise Exception("QuickBooks client not initialized")
            
            if active_only:
                accounts = Account.filter(Active=True, qb=self.qb_client)
            else:
                accounts = Account.all(qb=self.qb_client)
            
            logger.info(f"Retrieved {len(accounts)} accounts")
            return accounts
            
        except QuickbooksException as e:
            logger.error(f"QuickBooks error getting accounts: {e.message}")
            raise Exception(f"QuickBooks error: {e.message}")
        except Exception as e:
            logger.error(f"Error getting accounts: {str(e)}")
            raise
    
    def get_account_by_id(self, account_id):
        """Get a specific account by ID"""
        try:
            if not self.qb_client:
                raise Exception("QuickBooks client not initialized")
            
            account = Account.get(account_id, qb=self.qb_client)
            logger.info(f"Retrieved account: {account.Name} (ID: {account.Id})")
            return account
            
        except QuickbooksException as e:
            logger.error(f"QuickBooks error getting account: {e.message}")
            raise Exception(f"QuickBooks error: {e.message}")
        except Exception as e:
            logger.error(f"Error getting account: {str(e)}")
            raise
