{% extends "base.html" %}

{% block title %}OAuth Configuration - QuickB<PERSON>s Account Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h3 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    OAuth Configuration Required
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h5><i class="fas fa-info-circle"></i> Redirect URI Configuration Needed</h5>
                    <p>The QuickBooks OAuth error indicates that the redirect URI needs to be configured in your QuickBooks Developer Console.</p>
                </div>

                <h5>Current OAuth Configuration:</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Client ID:</th>
                            <td><code>{{ oauth_info.client_id }}</code></td>
                        </tr>
                        <tr>
                            <th>Redirect URI:</th>
                            <td><code>{{ oauth_info.redirect_uri }}</code></td>
                        </tr>
                        <tr>
                            <th>Environment:</th>
                            <td><span class="badge bg-info">{{ oauth_info.environment }}</span></td>
                        </tr>
                        <tr>
                            <th>Scopes:</th>
                            <td>
                                {% for scope in oauth_info.scopes %}
                                <span class="badge bg-secondary me-1">{{ scope }}</span>
                                {% endfor %}
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb"></i> How to Fix the Redirect URI Error:</h6>
                    <ol>
                        <li><strong>Go to QuickBooks Developer Console:</strong>
                            <br><a href="https://developer.intuit.com/app/developer/myapps" target="_blank" class="btn btn-sm btn-outline-primary mt-1">
                                <i class="fas fa-external-link-alt"></i> Open Developer Console
                            </a>
                        </li>
                        <li><strong>Find your app</strong> with Client ID: <code>{{ oauth_info.client_id }}</code></li>
                        <li><strong>Go to the "Keys & OAuth" tab</strong></li>
                        <li><strong>Add this Redirect URI:</strong>
                            <div class="mt-2">
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ oauth_info.redirect_uri }}" id="redirectUri" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('redirectUri')">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                            </div>
                        </li>
                        <li><strong>Save the changes</strong> in the QuickBooks Developer Console</li>
                        <li><strong>Try connecting again</strong></li>
                    </ol>
                </div>

                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Alternative: Test Without Full OAuth</h6>
                    <p>You can still test the detailed logging system without completing the OAuth flow:</p>
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="{{ url_for('test_logging') }}" class="btn btn-success">
                            <i class="fas fa-vial"></i> Test Detailed Logging
                        </a>
                        <a href="{{ url_for('view_logs') }}" class="btn btn-info">
                            <i class="fas fa-file-alt"></i> View Logs
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">What the Detailed Logging Shows</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>OAuth Flow Logging:</h6>
                        <ul class="list-unstyled small">
                            <li>✅ Complete authorization URLs</li>
                            <li>✅ All query parameters</li>
                            <li>✅ Client configuration</li>
                            <li>✅ Redirect URIs</li>
                            <li>✅ Scopes and permissions</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>API Request/Response Logging:</h6>
                        <ul class="list-unstyled small">
                            <li>✅ HTTP methods and URLs</li>
                            <li>✅ Request headers (sensitive data masked)</li>
                            <li>✅ Request bodies (JSON formatted)</li>
                            <li>✅ Response status codes</li>
                            <li>✅ Response data and timing</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="{{ url_for('index') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-home"></i> Back to Home
                </a>
                <a href="{{ url_for('test_logging') }}" class="btn btn-primary">
                    <i class="fas fa-play"></i> Test Logging Now
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Show feedback
        const button = element.nextElementSibling;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy: ', err);
    }
}
</script>
{% endblock %}
