2025-06-15 09:47:52,588 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:47:52,592 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:48:08,665 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:08,666 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:09,262 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:48:09,264 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:48:30,978 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:30,979 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:32,061 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:48:32,063 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:48:58,730 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:58,731 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:58,732 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:59,915 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:48:59,918 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:49:04,542 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:04] "GET / HTTP/1.1" 200 -
2025-06-15 09:49:04,666 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:04] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:49:27,032 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:49:27,035 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:49:27,036 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:49:28,056 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:49:28,059 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:49:42,499 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:49:42,503 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:49:43,804 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:49:43,807 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:49:49,037 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:49] "GET / HTTP/1.1" 200 -
2025-06-15 09:49:49,226 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:49] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:50:14,233 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:50:14,235 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:50:14,235 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:50:15,715 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:50:15,718 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:51:28,613 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-15 09:51:28,614 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 09:51:28,633 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:51:29,436 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:51:29,439 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:51:49,321 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:49] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:51:49,342 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:49] "GET / HTTP/1.1" 200 -
2025-06-15 09:51:55,807 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:55] "GET / HTTP/1.1" 200 -
2025-06-15 09:51:55,943 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:00,803 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:52:01,427 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:01] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:52:01,623 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:52:01,655 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\logging\\__init__.py', reloading
2025-06-15 09:52:01,714 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:52:01,715 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\encodings\\cp1252.py', reloading
2025-06-15 09:52:01,716 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\socketserver.py', reloading
2025-06-15 09:52:01,721 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:52:01,721 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\threading.py', reloading
2025-06-15 09:52:01,721 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:52:01,722 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:52:01,722 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:52:01,728 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:01] "GET / HTTP/1.1" 200 -
2025-06-15 09:52:01,736 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:52:01,742 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=VOTPXJeFmbU6n9SqtdvixvGT7EtwH5
2025-06-15 09:52:01,742 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:52:01,748 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:01] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:52:02,114 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:52:03,049 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:52:03,053 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:52:03,149 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:03] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:15,103 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:15] "GET / HTTP/1.1" 200 -
2025-06-15 09:52:15,395 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:24,033 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:24] "GET / HTTP/1.1" 200 -
2025-06-15 09:52:24,258 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:33,855 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:52:34,528 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:52:34,535 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:52:34,538 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:52:34,538 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:52:34,538 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:52:34,538 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:52:34,544 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:52:34,547 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=rs8VhGEahot4opxa4kmLfDlU7BaCfm
2025-06-15 09:52:34,548 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:52:34,550 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:34] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:52:35,906 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:35,907 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:35,907 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:36,504 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:52:37,564 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:52:37,568 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:52:48,493 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:48,494 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:48,495 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:48,804 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:52:49,461 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:52:49,464 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:52:59,110 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:59,112 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:59,113 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:59,749 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:01,009 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:01,014 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:10,161 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:10,162 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:10,283 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:10,944 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:10,947 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:10,988 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:10] "GET / HTTP/1.1" 200 -
2025-06-15 09:53:11,075 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:11] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:53:13,403 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:13] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:53:13,702 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:13] "GET / HTTP/1.1" 200 -
2025-06-15 09:53:14,031 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:53:21,170 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:21,174 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:22,154 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:22,748 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:22,750 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:31,646 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:31,647 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:31,648 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:31,909 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:32,452 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:32,455 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:42,434 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:42,437 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:42,614 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:43,197 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:43,199 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:52,253 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:52,257 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:52,258 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:52,362 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:52,945 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:52,947 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:03,440 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:03,443 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:03,444 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:04,108 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:04,670 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:04,673 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:13,391 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:13,392 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:13,393 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:13,832 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:14,391 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:14,395 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:25,578 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:25,582 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:25,583 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:26,575 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:27,232 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:27,236 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:35,488 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:35,492 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:36,434 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:37,319 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:37,324 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:46,301 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:46,302 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:46,506 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:47,184 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:47,189 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:57,229 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:57,232 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:57,233 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:57,370 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:57,957 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:57,960 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:06,508 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:06,511 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:06,511 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:07,134 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:07,749 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:07,751 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:17,357 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:17,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:17,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:17,924 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:18,526 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:18,529 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:40,946 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:40,947 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:40,947 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:41,810 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:42,366 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:42,370 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:51,151 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:51,155 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:51,525 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:52,070 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:52,073 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:56:08,990 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-15 09:56:08,990 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 09:56:09,002 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:56:09,683 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:56:09,686 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:56:32,178 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:32] "GET / HTTP/1.1" 200 -
2025-06-15 09:56:32,278 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:32] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:56:34,189 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:56:34,976 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:56:34,976 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,977 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:56:34,977 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,978 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:56:34,978 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:56:34,978 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:56:34,979 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:56:34,979 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,980 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,980 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:56:34,980 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,980 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=LxQDOUYzStkkQJtTd82XzuAnARju3X
2025-06-15 09:56:34,981 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:56:34,981 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,983 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:34] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:56:41,941 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,941 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:56:41,942 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,942 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:56:41,942 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:56:41,942 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:56:41,942 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:56:41,942 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:56:41,943 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=LxQDOUYzStkkQJtTd82XzuAnARju3X
2025-06-15 09:56:41,943 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:56:41,943 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:41] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:57:13,684 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:13] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:57:13,688 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:13] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:18,790 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:18] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:19,094 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:57:22,322 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:22] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:57:22,418 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:22] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:22,551 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:57:33,285 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:33] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:57:33,595 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:33] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:33,948 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:33] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:57:46,577 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:57:46,580 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:57:46,581 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:57:46,701 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:57:47,294 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:57:47,297 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:58:28,243 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:58:28,844 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:58:28,845 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,845 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:58:28,845 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,845 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:58:28,845 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:58:28,845 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:58:28,846 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,846 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,846 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=j5SaIDzIv7FVe3fh45h2FOgWaTXHmL
2025-06-15 09:58:28,846 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,848 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:28] "[32mGET /test_logging HTTP/1.1[0m" 302 -
2025-06-15 09:58:28,887 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:28] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:58:28,892 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:28] "GET / HTTP/1.1" 200 -
2025-06-15 09:58:45,014 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:58:45,711 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:58:45,712 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,712 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:58:45,712 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,712 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:58:45,713 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:58:45,713 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:58:45,713 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:58:45,713 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,713 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,714 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:58:45,714 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,714 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=2ricfWv31m6WyFakyWJIcI9Fevidm8
2025-06-15 09:58:45,714 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:58:45,714 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,716 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:45] "[32mGET /test_logging HTTP/1.1[0m" 302 -
2025-06-15 09:58:45,736 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:45] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:58:45,741 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:45] "GET / HTTP/1.1" 200 -
2025-06-15 09:58:46,192 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:46] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:58:55,630 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:55] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:58:55,931 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:55] "GET / HTTP/1.1" 200 -
2025-06-15 09:58:56,275 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:59:30,971 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:30] "GET / HTTP/1.1" 200 -
2025-06-15 09:59:31,276 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:31] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:59:33,882 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:33] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:59:34,094 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:34] "GET / HTTP/1.1" 200 -
2025-06-15 09:59:34,133 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:34] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:59:36,259 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:59:36,984 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:59:36,985 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:59:36,985 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:59:36,985 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:59:36,985 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:59:36,986 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:59:36,986 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:59:36,986 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:59:36,986 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:59:36,986 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:59:36,986 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:59:36,986 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:59:36,987 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=GH7U6x6O2jmvNQcFvqOL3WU71eCBoq
2025-06-15 09:59:36,987 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:59:36,987 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:59:36,988 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:36] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 10:00:09,981 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\config.py', reloading
2025-06-15 10:00:09,982 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\config.py', reloading
2025-06-15 10:00:09,982 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\config.py', reloading
2025-06-15 10:00:10,608 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 10:00:11,422 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 10:00:11,426 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 10:00:22,397 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\config.py', reloading
2025-06-15 10:00:22,401 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\config.py', reloading
2025-06-15 10:00:22,401 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\config.py', reloading
2025-06-15 10:00:22,606 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 10:00:23,207 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 10:00:23,210 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 10:00:45,812 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:00:45] "GET /logs HTTP/1.1" 200 -
2025-06-15 10:01:02,486 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 10:01:02,487 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 10:01:02,488 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 10:01:02,664 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 10:01:03,466 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 10:01:03,470 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 10:01:43,816 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 10:01:43,820 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 10:01:43,821 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 10:01:44,181 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 10:01:44,806 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 10:01:44,809 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 10:02:06,263 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:06] "GET /logs HTTP/1.1" 200 -
2025-06-15 10:02:06,364 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:06] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:02:11,693 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:11] "GET /oauth_info HTTP/1.1" 200 -
2025-06-15 10:02:17,382 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:02:18,082 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:02:18,097 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:02:18,106 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 10:02:18,111 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:02:18,115 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 10:02:18,120 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 10:02:18,125 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 10:02:18,128 - quickbooks_client - INFO - environment: sandbox
2025-06-15 10:02:18,139 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:02:18,147 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:02:18,155 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 10:02:18,157 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:02:18,158 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=wUFoKpx2yk8G5vqKtSUL9fLqPgl2qA
2025-06-15 10:02:18,160 - quickbooks_client - INFO - url_length: 253
2025-06-15 10:02:18,160 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:02:18,177 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:18] "[32mGET /test_logging HTTP/1.1[0m" 302 -
2025-06-15 10:02:18,224 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:18] "GET /logs HTTP/1.1" 200 -
2025-06-15 10:02:22,238 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:22] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:02:28,895 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:28] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:02:34,309 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:34] "GET /oauth_info HTTP/1.1" 200 -
2025-06-15 10:02:34,627 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:34] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:02:39,496 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:02:44,176 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:02:49,357 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:02:54,069 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:02:59,352 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:02:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:04,056 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:09,358 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:14,071 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:19,353 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:24,173 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:29,476 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:33,466 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:33] "GET / HTTP/1.1" 200 -
2025-06-15 10:03:33,766 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:33] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:03:34,498 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:39,055 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:40,705 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:03:41,236 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:03:41,237 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:03:41,237 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 10:03:41,237 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:03:41,237 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 10:03:41,238 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 10:03:41,238 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 10:03:41,238 - quickbooks_client - INFO - environment: sandbox
2025-06-15 10:03:41,238 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:03:41,238 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:03:41,238 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 10:03:41,239 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:03:41,239 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=3V77Gs4D1qhwC8k8OWmAtpODWx9ple
2025-06-15 10:03:41,239 - quickbooks_client - INFO - url_length: 253
2025-06-15 10:03:41,239 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:03:41,239 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:41] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 10:03:44,066 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:49,058 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:54,384 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:03:59,052 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:03:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:04,381 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:09,046 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:14,378 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:19,055 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:24,384 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:29,164 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:34,487 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:39,172 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:44,484 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:49,173 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:54,508 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:04:59,175 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:04:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:04,501 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:09,165 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:14,483 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:19,161 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:24,500 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:26,420 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:26] "GET /logs HTTP/1.1" 200 -
2025-06-15 10:05:26,729 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:26] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:05:27,736 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:27] "GET /oauth_info HTTP/1.1" 200 -
2025-06-15 10:05:27,962 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:27] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:05:34,777 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:39,494 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:42,146 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:42] "GET / HTTP/1.1" 200 -
2025-06-15 10:05:42,458 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:42] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:05:44,507 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:49,059 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:51,998 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:51,998 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 10:05:51,998 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:51,998 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 10:05:51,998 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 10:05:51,999 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 10:05:51,999 - quickbooks_client - INFO - environment: sandbox
2025-06-15 10:05:51,999 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:51,999 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:51,999 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 10:05:51,999 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:51,999 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=3V77Gs4D1qhwC8k8OWmAtpODWx9ple
2025-06-15 10:05:52,000 - quickbooks_client - INFO - url_length: 253
2025-06-15 10:05:52,000 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:52,000 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:52] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 10:05:54,071 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:05:54,910 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:54,911 - quickbooks_client - INFO - OAUTH STEP: EXCHANGE_AUTH_CODE
2025-06-15 10:05:54,911 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:54,911 - quickbooks_client - INFO - auth_code: XAB1175000...
2025-06-15 10:05:54,911 - quickbooks_client - INFO - realm_id: ****************
2025-06-15 10:05:54,911 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 10:05:54,911 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:54,913 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): oauth.platform.intuit.com:443
2025-06-15 10:05:55,582 - urllib3.connectionpool - DEBUG - https://oauth.platform.intuit.com:443 "POST /oauth2/v1/tokens/bearer HTTP/1.1" 200 759
2025-06-15 10:05:55,583 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:55,583 - quickbooks_client - INFO - OAUTH STEP: TOKEN_EXCHANGE_SUCCESS
2025-06-15 10:05:55,583 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:55,584 - quickbooks_client - INFO - company_id: ****************
2025-06-15 10:05:55,584 - quickbooks_client - INFO - access_token: ********************
2025-06-15 10:05:55,584 - quickbooks_client - INFO - refresh_token: ********************
2025-06-15 10:05:55,584 - quickbooks_client - INFO - token_type: Bearer
2025-06-15 10:05:55,584 - quickbooks_client - INFO - expires_in: 3600
2025-06-15 10:05:55,584 - quickbooks_client - INFO - duration: 0:00:00.671202
2025-06-15 10:05:55,584 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 10:05:55,585 - quickbooks_client - INFO - QuickBooks client initialized successfully
2025-06-15 10:05:55,585 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:55] "[32mGET /callback?code=XAB11750000554NVKqQk5riXYy7sRjEeA88TUF1TpnKk96tU5k&state=3V77Gs4D1qhwC8k8OWmAtpODWx9ple&realmId=**************** HTTP/1.1[0m" 302 -
2025-06-15 10:05:55,590 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:55] "GET / HTTP/1.1" 200 -
2025-06-15 10:05:55,941 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:05:59,056 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:05:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:04,377 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:06,875 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:06:07,423 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:06:07,426 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:06:07,426 - quickbooks_client - INFO - RETRIEVING QUICKBOOKS ACCOUNTS
2025-06-15 10:06:07,426 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:06:07,426 - quickbooks_client - INFO - Filter: Active accounts only
2025-06-15 10:06:07,426 - quickbooks_client - INFO - Making API call: Account.filter(Active=True)
2025-06-15 10:06:07,427 - requests_oauthlib.oauth2_session - DEBUG - Invoking 0 protected resource request hooks.
2025-06-15 10:06:07,427 - requests_oauthlib.oauth2_session - DEBUG - Adding token {'access_token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA', 'refresh_token': None} to request.
2025-06-15 10:06:07,427 - requests_oauthlib.oauth2_session - DEBUG - Requesting url https://sandbox-quickbooks.api.intuit.com/v3/company/****************/query using method POST.
2025-06-15 10:06:07,427 - requests_oauthlib.oauth2_session - DEBUG - Supplying headers {'Content-Type': 'application/text', 'Accept': 'application/json', 'User-Agent': 'python-quickbooks V3 library', 'Authorization': 'Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA'} and data SELECT * FROM Account WHERE Active = True
2025-06-15 10:06:07,427 - requests_oauthlib.oauth2_session - DEBUG - Passing through key word arguments {'params': {'minorversion': 75}}.
2025-06-15 10:06:07,428 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): sandbox-quickbooks.api.intuit.com:443
2025-06-15 10:06:08,982 - urllib3.connectionpool - DEBUG - https://sandbox-quickbooks.api.intuit.com:443 "POST /v3/company/****************/query?minorversion=75 HTTP/1.1" 200 3337
2025-06-15 10:06:08,987 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:06:08,987 - quickbooks_client - INFO - ACCOUNTS RETRIEVAL SUCCESS
2025-06-15 10:06:08,988 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:06:08,988 - quickbooks_client - INFO - Total accounts retrieved: 89
2025-06-15 10:06:08,989 - quickbooks_client - INFO - Retrieval duration: 0:00:01.560636
2025-06-15 10:06:08,989 - quickbooks_client - INFO - Account breakdown by type:
2025-06-15 10:06:08,989 - quickbooks_client - INFO -   Expense: 44
2025-06-15 10:06:08,989 - quickbooks_client - INFO -   Accounts Payable: 1
2025-06-15 10:06:08,990 - quickbooks_client - INFO -   Accounts Receivable: 1
2025-06-15 10:06:08,990 - quickbooks_client - INFO -   Other Current Liability: 3
2025-06-15 10:06:08,990 - quickbooks_client - INFO -   Income: 20
2025-06-15 10:06:08,991 - quickbooks_client - INFO -   Bank: 2
2025-06-15 10:06:08,991 - quickbooks_client - INFO -   Cost of Goods Sold: 1
2025-06-15 10:06:08,991 - quickbooks_client - INFO -   Other Expense: 3
2025-06-15 10:06:08,991 - quickbooks_client - INFO -   Fixed Asset: 3
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   Other Income: 2
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   Other Current Asset: 4
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   Credit Card: 2
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   Long Term Liability: 1
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   Equity: 2
2025-06-15 10:06:08,992 - quickbooks_client - INFO - Sample accounts:
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   1. Accounting (Expense) - ID: 69
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   2. Accounts Payable (A/P) (Accounts Payable) - ID: 33
2025-06-15 10:06:08,992 - quickbooks_client - INFO -   3. Accounts Receivable (A/R) (Accounts Receivable) - ID: 84
2025-06-15 10:06:08,993 - quickbooks_client - INFO -   4. Advertising (Expense) - ID: 7
2025-06-15 10:06:08,993 - quickbooks_client - INFO -   5. Arizona Dept. of Revenue Payable (Other Current Liability) - ID: 89
2025-06-15 10:06:08,993 - quickbooks_client - INFO -   ... and 84 more accounts
2025-06-15 10:06:08,993 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:06:09,000 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:09] "GET /view_accounts HTTP/1.1" 200 -
2025-06-15 10:06:09,058 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:09] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:06:09,389 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:14,050 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:19,381 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:24,175 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:29,498 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:34,173 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:39,508 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:44,177 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:49,484 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:54,175 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:06:59,512 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:06:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:07:04,173 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:07:09,497 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:07:14,175 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:07:19,511 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:07:36,622 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:07:37,123 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:07:37,126 - requests_oauthlib.oauth2_session - DEBUG - Invoking 0 protected resource request hooks.
2025-06-15 10:07:37,127 - requests_oauthlib.oauth2_session - DEBUG - Adding token {'access_token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA', 'refresh_token': None} to request.
2025-06-15 10:07:37,127 - requests_oauthlib.oauth2_session - DEBUG - Requesting url https://sandbox-quickbooks.api.intuit.com/v3/company/****************/account/69 using method GET.
2025-06-15 10:07:37,127 - requests_oauthlib.oauth2_session - DEBUG - Supplying headers {'Content-Type': 'application/json', 'Accept': 'application/json', 'User-Agent': 'python-quickbooks V3 library', 'Authorization': 'Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA'} and data {}
2025-06-15 10:07:37,127 - requests_oauthlib.oauth2_session - DEBUG - Passing through key word arguments {'params': {'minorversion': 75}}.
2025-06-15 10:07:37,128 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): sandbox-quickbooks.api.intuit.com:443
2025-06-15 10:07:38,172 - urllib3.connectionpool - DEBUG - https://sandbox-quickbooks.api.intuit.com:443 "GET /v3/company/****************/account/69?minorversion=75 HTTP/1.1" 200 323
2025-06-15 10:07:38,173 - quickbooks_client - INFO - Retrieved account: Accounting (ID: 69)
2025-06-15 10:07:38,184 - __main__ - ERROR - Error getting account details: 'str object' has no attribute 'strftime'
2025-06-15 10:07:38,185 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:38] "[32mGET /account/69 HTTP/1.1[0m" 302 -
2025-06-15 10:07:38,192 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:07:38,703 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:07:38,706 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:07:38,706 - quickbooks_client - INFO - RETRIEVING QUICKBOOKS ACCOUNTS
2025-06-15 10:07:38,707 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:07:38,707 - quickbooks_client - INFO - Filter: Active accounts only
2025-06-15 10:07:38,707 - quickbooks_client - INFO - Making API call: Account.filter(Active=True)
2025-06-15 10:07:38,707 - requests_oauthlib.oauth2_session - DEBUG - Invoking 0 protected resource request hooks.
2025-06-15 10:07:38,707 - requests_oauthlib.oauth2_session - DEBUG - Adding token {'access_token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA', 'refresh_token': None} to request.
2025-06-15 10:07:38,707 - requests_oauthlib.oauth2_session - DEBUG - Requesting url https://sandbox-quickbooks.api.intuit.com/v3/company/****************/query using method POST.
2025-06-15 10:07:38,707 - requests_oauthlib.oauth2_session - DEBUG - Supplying headers {'Content-Type': 'application/text', 'Accept': 'application/json', 'User-Agent': 'python-quickbooks V3 library', 'Authorization': 'Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA'} and data SELECT * FROM Account WHERE Active = True
2025-06-15 10:07:38,707 - requests_oauthlib.oauth2_session - DEBUG - Passing through key word arguments {'params': {'minorversion': 75}}.
2025-06-15 10:07:38,708 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): sandbox-quickbooks.api.intuit.com:443
2025-06-15 10:07:39,773 - urllib3.connectionpool - DEBUG - https://sandbox-quickbooks.api.intuit.com:443 "POST /v3/company/****************/query?minorversion=75 HTTP/1.1" 200 3338
2025-06-15 10:07:39,774 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:07:39,774 - quickbooks_client - INFO - ACCOUNTS RETRIEVAL SUCCESS
2025-06-15 10:07:39,775 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:07:39,775 - quickbooks_client - INFO - Total accounts retrieved: 89
2025-06-15 10:07:39,775 - quickbooks_client - INFO - Retrieval duration: 0:00:01.067477
2025-06-15 10:07:39,775 - quickbooks_client - INFO - Account breakdown by type:
2025-06-15 10:07:39,775 - quickbooks_client - INFO -   Expense: 44
2025-06-15 10:07:39,775 - quickbooks_client - INFO -   Accounts Payable: 1
2025-06-15 10:07:39,775 - quickbooks_client - INFO -   Accounts Receivable: 1
2025-06-15 10:07:39,775 - quickbooks_client - INFO -   Other Current Liability: 3
2025-06-15 10:07:39,775 - quickbooks_client - INFO -   Income: 20
2025-06-15 10:07:39,775 - quickbooks_client - INFO -   Bank: 2
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Cost of Goods Sold: 1
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Other Expense: 3
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Fixed Asset: 3
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Other Income: 2
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Other Current Asset: 4
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Credit Card: 2
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Long Term Liability: 1
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   Equity: 2
2025-06-15 10:07:39,776 - quickbooks_client - INFO - Sample accounts:
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   1. Accounting (Expense) - ID: 69
2025-06-15 10:07:39,776 - quickbooks_client - INFO -   2. Accounts Payable (A/P) (Accounts Payable) - ID: 33
2025-06-15 10:07:39,777 - quickbooks_client - INFO -   3. Accounts Receivable (A/R) (Accounts Receivable) - ID: 84
2025-06-15 10:07:39,777 - quickbooks_client - INFO -   4. Advertising (Expense) - ID: 7
2025-06-15 10:07:39,777 - quickbooks_client - INFO -   5. Arizona Dept. of Revenue Payable (Other Current Liability) - ID: 89
2025-06-15 10:07:39,777 - quickbooks_client - INFO -   ... and 84 more accounts
2025-06-15 10:07:39,777 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:07:39,779 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:39] "GET /view_accounts HTTP/1.1" 200 -
2025-06-15 10:07:40,138 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:40] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:07:44,180 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:07:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:08:40,742 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:08:40] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:08:44,189 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:08:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:08:49,486 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:08:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:08:54,181 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:08:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:08:59,479 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:08:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:04,176 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:09,483 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:14,180 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:19,469 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:24,179 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:29,483 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:34,175 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:39,470 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:09:44,177 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:09:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:10:44,471 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:10:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:11:44,189 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:11:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:12:44,492 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:12:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:34,362 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:39,487 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:44,186 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:49,487 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:51,924 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:51] "GET /create_account HTTP/1.1" 200 -
2025-06-15 10:13:52,237 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:52] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:13:54,498 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:54,739 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:54] "GET / HTTP/1.1" 200 -
2025-06-15 10:13:54,921 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:54] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:13:57,354 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:13:57,875 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:13:57,878 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:13:57,879 - quickbooks_client - INFO - RETRIEVING QUICKBOOKS ACCOUNTS
2025-06-15 10:13:57,879 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:13:57,879 - quickbooks_client - INFO - Filter: Active accounts only
2025-06-15 10:13:57,879 - quickbooks_client - INFO - Making API call: Account.filter(Active=True)
2025-06-15 10:13:57,880 - requests_oauthlib.oauth2_session - DEBUG - Invoking 0 protected resource request hooks.
2025-06-15 10:13:57,880 - requests_oauthlib.oauth2_session - DEBUG - Adding token {'access_token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA', 'refresh_token': None} to request.
2025-06-15 10:13:57,880 - requests_oauthlib.oauth2_session - DEBUG - Requesting url https://sandbox-quickbooks.api.intuit.com/v3/company/****************/query using method POST.
2025-06-15 10:13:57,880 - requests_oauthlib.oauth2_session - DEBUG - Supplying headers {'Content-Type': 'application/text', 'Accept': 'application/json', 'User-Agent': 'python-quickbooks V3 library', 'Authorization': 'Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA'} and data SELECT * FROM Account WHERE Active = True
2025-06-15 10:13:57,880 - requests_oauthlib.oauth2_session - DEBUG - Passing through key word arguments {'params': {'minorversion': 75}}.
2025-06-15 10:13:57,881 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): sandbox-quickbooks.api.intuit.com:443
2025-06-15 10:13:59,185 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:13:59,226 - urllib3.connectionpool - DEBUG - https://sandbox-quickbooks.api.intuit.com:443 "POST /v3/company/****************/query?minorversion=75 HTTP/1.1" 200 3337
2025-06-15 10:13:59,228 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:13:59,228 - quickbooks_client - INFO - ACCOUNTS RETRIEVAL SUCCESS
2025-06-15 10:13:59,228 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:13:59,229 - quickbooks_client - INFO - Total accounts retrieved: 89
2025-06-15 10:13:59,229 - quickbooks_client - INFO - Retrieval duration: 0:00:01.348368
2025-06-15 10:13:59,229 - quickbooks_client - INFO - Account breakdown by type:
2025-06-15 10:13:59,229 - quickbooks_client - INFO -   Expense: 44
2025-06-15 10:13:59,229 - quickbooks_client - INFO -   Accounts Payable: 1
2025-06-15 10:13:59,230 - quickbooks_client - INFO -   Accounts Receivable: 1
2025-06-15 10:13:59,230 - quickbooks_client - INFO -   Other Current Liability: 3
2025-06-15 10:13:59,230 - quickbooks_client - INFO -   Income: 20
2025-06-15 10:13:59,230 - quickbooks_client - INFO -   Bank: 2
2025-06-15 10:13:59,230 - quickbooks_client - INFO -   Cost of Goods Sold: 1
2025-06-15 10:13:59,230 - quickbooks_client - INFO -   Other Expense: 3
2025-06-15 10:13:59,231 - quickbooks_client - INFO -   Fixed Asset: 3
2025-06-15 10:13:59,231 - quickbooks_client - INFO -   Other Income: 2
2025-06-15 10:13:59,231 - quickbooks_client - INFO -   Other Current Asset: 4
2025-06-15 10:13:59,231 - quickbooks_client - INFO -   Credit Card: 2
2025-06-15 10:13:59,231 - quickbooks_client - INFO -   Long Term Liability: 1
2025-06-15 10:13:59,231 - quickbooks_client - INFO -   Equity: 2
2025-06-15 10:13:59,231 - quickbooks_client - INFO - Sample accounts:
2025-06-15 10:13:59,232 - quickbooks_client - INFO -   1. Accounting (Expense) - ID: 69
2025-06-15 10:13:59,232 - quickbooks_client - INFO -   2. Accounts Payable (A/P) (Accounts Payable) - ID: 33
2025-06-15 10:13:59,232 - quickbooks_client - INFO -   3. Accounts Receivable (A/R) (Accounts Receivable) - ID: 84
2025-06-15 10:13:59,232 - quickbooks_client - INFO -   4. Advertising (Expense) - ID: 7
2025-06-15 10:13:59,232 - quickbooks_client - INFO -   5. Arizona Dept. of Revenue Payable (Other Current Liability) - ID: 89
2025-06-15 10:13:59,232 - quickbooks_client - INFO -   ... and 84 more accounts
2025-06-15 10:13:59,232 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:13:59,234 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:59] "GET /view_accounts HTTP/1.1" 200 -
2025-06-15 10:13:59,569 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:13:59] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:02,314 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:02] "GET /logs HTTP/1.1" 200 -
2025-06-15 10:14:02,621 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:02] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:04,169 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:04] "GET /oauth_info HTTP/1.1" 200 -
2025-06-15 10:14:04,302 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:04] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:04,461 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:06,247 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:14:06,790 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:14:06,793 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:14:06,794 - quickbooks_client - INFO - RETRIEVING QUICKBOOKS ACCOUNTS
2025-06-15 10:14:06,794 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:14:06,795 - quickbooks_client - INFO - Filter: Active accounts only
2025-06-15 10:14:06,795 - quickbooks_client - INFO - Making API call: Account.filter(Active=True)
2025-06-15 10:14:06,795 - requests_oauthlib.oauth2_session - DEBUG - Invoking 0 protected resource request hooks.
2025-06-15 10:14:06,795 - requests_oauthlib.oauth2_session - DEBUG - Adding token {'access_token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA', 'refresh_token': None} to request.
2025-06-15 10:14:06,796 - requests_oauthlib.oauth2_session - DEBUG - Requesting url https://sandbox-quickbooks.api.intuit.com/v3/company/****************/query using method POST.
2025-06-15 10:14:06,796 - requests_oauthlib.oauth2_session - DEBUG - Supplying headers {'Content-Type': 'application/text', 'Accept': 'application/json', 'User-Agent': 'python-quickbooks V3 library', 'Authorization': 'Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA'} and data SELECT * FROM Account WHERE Active = True
2025-06-15 10:14:06,796 - requests_oauthlib.oauth2_session - DEBUG - Passing through key word arguments {'params': {'minorversion': 75}}.
2025-06-15 10:14:06,797 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): sandbox-quickbooks.api.intuit.com:443
2025-06-15 10:14:07,843 - urllib3.connectionpool - DEBUG - https://sandbox-quickbooks.api.intuit.com:443 "POST /v3/company/****************/query?minorversion=75 HTTP/1.1" 200 3337
2025-06-15 10:14:07,844 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:14:07,845 - quickbooks_client - INFO - ACCOUNTS RETRIEVAL SUCCESS
2025-06-15 10:14:07,845 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:14:07,845 - quickbooks_client - INFO - Total accounts retrieved: 89
2025-06-15 10:14:07,845 - quickbooks_client - INFO - Retrieval duration: 0:00:01.049742
2025-06-15 10:14:07,846 - quickbooks_client - INFO - Account breakdown by type:
2025-06-15 10:14:07,846 - quickbooks_client - INFO -   Expense: 44
2025-06-15 10:14:07,846 - quickbooks_client - INFO -   Accounts Payable: 1
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Accounts Receivable: 1
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Other Current Liability: 3
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Income: 20
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Bank: 2
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Cost of Goods Sold: 1
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Other Expense: 3
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Fixed Asset: 3
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Other Income: 2
2025-06-15 10:14:07,847 - quickbooks_client - INFO -   Other Current Asset: 4
2025-06-15 10:14:07,848 - quickbooks_client - INFO -   Credit Card: 2
2025-06-15 10:14:07,848 - quickbooks_client - INFO -   Long Term Liability: 1
2025-06-15 10:14:07,848 - quickbooks_client - INFO -   Equity: 2
2025-06-15 10:14:07,848 - quickbooks_client - INFO - Sample accounts:
2025-06-15 10:14:07,848 - quickbooks_client - INFO -   1. Accounting (Expense) - ID: 69
2025-06-15 10:14:07,848 - quickbooks_client - INFO -   2. Accounts Payable (A/P) (Accounts Payable) - ID: 33
2025-06-15 10:14:07,849 - quickbooks_client - INFO -   3. Accounts Receivable (A/R) (Accounts Receivable) - ID: 84
2025-06-15 10:14:07,849 - quickbooks_client - INFO -   4. Advertising (Expense) - ID: 7
2025-06-15 10:14:07,849 - quickbooks_client - INFO -   5. Arizona Dept. of Revenue Payable (Other Current Liability) - ID: 89
2025-06-15 10:14:07,849 - quickbooks_client - INFO -   ... and 84 more accounts
2025-06-15 10:14:07,849 - quickbooks_client - INFO - [ACCOUNTS] ==================================================================
2025-06-15 10:14:07,851 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:07] "GET /view_accounts HTTP/1.1" 200 -
2025-06-15 10:14:07,906 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:07] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:09,506 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:10,143 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:10] "GET /create_account HTTP/1.1" 200 -
2025-06-15 10:14:10,446 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:14,512 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:19,169 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:24,504 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:29,165 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:34,488 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 10:14:34,514 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:14:35,016 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 10:14:35,024 - quickbooks_client - INFO - [ACCOUNT] ====================================================================
2025-06-15 10:14:35,024 - quickbooks_client - INFO - CREATING QUICKBOOKS ACCOUNT
2025-06-15 10:14:35,025 - quickbooks_client - INFO - [ACCOUNT] ====================================================================
2025-06-15 10:14:35,025 - quickbooks_client - INFO - Account Data: {
  "name": "Demo",
  "account_type": "Income",
  "account_sub_type": "Income",
  "description": "Cool incoming money",
  "account_number": "112233"
}
2025-06-15 10:14:35,025 - quickbooks_client - INFO - Account object created:
2025-06-15 10:14:35,025 - quickbooks_client - INFO -   Name: Demo
2025-06-15 10:14:35,025 - quickbooks_client - INFO -   Type: Income
2025-06-15 10:14:35,025 - quickbooks_client - INFO -   SubType: Income
2025-06-15 10:14:35,025 - quickbooks_client - INFO -   Description: Cool incoming money
2025-06-15 10:14:35,026 - quickbooks_client - INFO -   Account Number: 112233
2025-06-15 10:14:35,026 - requests_oauthlib.oauth2_session - DEBUG - Invoking 0 protected resource request hooks.
2025-06-15 10:14:35,026 - requests_oauthlib.oauth2_session - DEBUG - Adding token {'access_token': 'eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA', 'refresh_token': None} to request.
2025-06-15 10:14:35,027 - requests_oauthlib.oauth2_session - DEBUG - Requesting url https://sandbox-quickbooks.api.intuit.com/v3/company/****************/account using method POST.
2025-06-15 10:14:35,027 - requests_oauthlib.oauth2_session - DEBUG - Supplying headers {'Content-Type': 'application/json', 'Accept': 'application/json', 'User-Agent': 'python-quickbooks V3 library', 'Authorization': 'Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..Yg9Hwf_rpT7P-iuX_vZ_tg.aL5_uDuoDsBoazXKL3hgj-J0nXVSdP2v-gsMMuvZVkfQV4wythW7XF4-v0xihtSKZCL1tuZav3msNatUZr42b2gntDs7BZI2dDBAvlEZjQ8omuy4g4KlBffQdtWF1eH5L1BhUaHHN4KCtihLJH3aTkt4np5MvSyxWGrxVQEpQuhYySDSZqyTrGz1NownAabsOPOcn9ipVQcJfoSsiQdDltc-5De9OeWpSG0kxEvhwssiB3RwSby6ZyyOc0eE2ydLZ10TuDenu73ncxnTuEbkxzBP85RNk9-JZ8figskeYiLjgzgR4PJ3tEXj_Kuv9OrrTqHsO5R4Y-WzzveX4gGEA51j4vgga5TKtFzOVmzSABJ26YjX_BFhRdzzUIPBBGFyV7L3pFNPVvoWCSTPqmjohMKhcsh1AfFsa9uR79Oj1AKh3uMHHmH7cAAX_l--TYK6jnoAYuZbQX-zWTvrHSzTYvJKYG6l-7t25F1vdXcHb5c.zHzqSYCIa4EJmD6Q2LNVIA'} and data {
    "AccountSubType": "Income",
    "AccountType": "Income",
    "AcctNum": "112233",
    "Active": true,
    "Description": "Cool incoming money",
    "FullyQualifiedName": "",
    "Name": "Demo",
    "SubAccount": false,
    "SyncToken": 0,
    "domain": "QBO",
    "sparse": false
}
2025-06-15 10:14:35,027 - requests_oauthlib.oauth2_session - DEBUG - Passing through key word arguments {'params': {'minorversion': 75}}.
2025-06-15 10:14:35,028 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): sandbox-quickbooks.api.intuit.com:443
2025-06-15 10:14:35,823 - urllib3.connectionpool - DEBUG - https://sandbox-quickbooks.api.intuit.com:443 "POST /v3/company/****************/account?minorversion=75 HTTP/1.1" 400 172
2025-06-15 10:14:35,824 - quickbooks_client - ERROR - [ACCOUNT] ====================================================================
2025-06-15 10:14:35,824 - quickbooks_client - ERROR - QUICKBOOKS API ERROR - ACCOUNT CREATION
2025-06-15 10:14:35,824 - quickbooks_client - ERROR - [ACCOUNT] ====================================================================
2025-06-15 10:14:35,825 - quickbooks_client - ERROR - Error Code: 2170
2025-06-15 10:14:35,825 - quickbooks_client - ERROR - Error Message: Invalid Enumeration
2025-06-15 10:14:35,825 - quickbooks_client - ERROR - Error Detail: Invalid Enumeration : Income
2025-06-15 10:14:35,826 - quickbooks_client - ERROR - [ACCOUNT] ====================================================================
2025-06-15 10:14:35,826 - __main__ - ERROR - Error creating account: QuickBooks error: Invalid Enumeration
2025-06-15 10:14:35,827 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:35] "POST /create_account HTTP/1.1" 200 -
2025-06-15 10:14:35,875 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:35] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:43,136 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:43] "GET /logs HTTP/1.1" 200 -
2025-06-15 10:14:43,268 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:43] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 10:14:44,174 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:14:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:15:44,472 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:15:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:16:44,181 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:16:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:25,336 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:25] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:29,056 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:34,366 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:39,058 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:44,353 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:49,069 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:54,360 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:17:59,073 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:17:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:04,362 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:09,066 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:09] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:14,369 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:14] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:19,072 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:19] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:24,363 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:24] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:29,071 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:29] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:34,368 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:34] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:39,065 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:39] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:44,366 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:44] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:49,071 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:49] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:54,374 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:54] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:18:59,061 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:18:59] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:19:04,367 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:19:04] "GET /api/logs HTTP/1.1" 200 -
2025-06-15 10:19:09,071 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 10:19:09] "GET /api/logs HTTP/1.1" 200 -
