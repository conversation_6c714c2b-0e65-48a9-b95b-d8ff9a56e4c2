2025-06-15 09:47:52,588 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:47:52,592 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:48:08,665 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:08,666 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:09,262 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:48:09,264 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:48:30,978 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:30,979 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:32,061 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:48:32,063 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:48:58,730 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:58,731 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:58,732 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:48:59,915 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:48:59,918 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:49:04,542 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:04] "GET / HTTP/1.1" 200 -
2025-06-15 09:49:04,666 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:04] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:49:27,032 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:49:27,035 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:49:27,036 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:49:28,056 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:49:28,059 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:49:42,499 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:49:42,503 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:49:43,804 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:49:43,807 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:49:49,037 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:49] "GET / HTTP/1.1" 200 -
2025-06-15 09:49:49,226 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:49:49] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:50:14,233 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:50:14,235 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:50:14,235 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:50:15,715 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:50:15,718 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:51:28,613 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-15 09:51:28,614 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 09:51:28,633 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:51:29,436 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:51:29,439 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:51:49,321 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:49] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:51:49,342 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:49] "GET / HTTP/1.1" 200 -
2025-06-15 09:51:55,807 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:55] "GET / HTTP/1.1" 200 -
2025-06-15 09:51:55,943 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:51:55] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:00,803 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:52:01,427 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:01] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:52:01,623 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:52:01,655 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\logging\\__init__.py', reloading
2025-06-15 09:52:01,714 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:52:01,715 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\encodings\\cp1252.py', reloading
2025-06-15 09:52:01,716 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\socketserver.py', reloading
2025-06-15 09:52:01,721 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:52:01,721 - werkzeug - INFO -  * Detected change in 'C:\\Python313\\Lib\\threading.py', reloading
2025-06-15 09:52:01,721 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:52:01,722 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:52:01,722 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:52:01,728 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:01] "GET / HTTP/1.1" 200 -
2025-06-15 09:52:01,736 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:52:01,742 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=VOTPXJeFmbU6n9SqtdvixvGT7EtwH5
2025-06-15 09:52:01,742 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:52:01,748 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:01] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:52:02,114 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:52:03,049 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:52:03,053 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:52:03,149 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:03] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:15,103 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:15] "GET / HTTP/1.1" 200 -
2025-06-15 09:52:15,395 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:15] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:24,033 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:24] "GET / HTTP/1.1" 200 -
2025-06-15 09:52:24,258 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:24] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:52:33,855 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:52:34,528 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:52:34,535 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:52:34,538 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:52:34,538 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:52:34,538 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:52:34,538 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:52:34,544 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:52:34,547 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=rs8VhGEahot4opxa4kmLfDlU7BaCfm
2025-06-15 09:52:34,548 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:52:34,550 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:52:34] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:52:35,906 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:35,907 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:35,907 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:36,504 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:52:37,564 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:52:37,568 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:52:48,493 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:48,494 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:48,495 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:48,804 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:52:49,461 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:52:49,464 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:52:59,110 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:59,112 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:59,113 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:52:59,749 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:01,009 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:01,014 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:10,161 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:10,162 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:10,283 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:10,944 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:10,947 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:10,988 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:10] "GET / HTTP/1.1" 200 -
2025-06-15 09:53:11,075 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:11] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:53:13,403 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:13] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:53:13,702 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:13] "GET / HTTP/1.1" 200 -
2025-06-15 09:53:14,031 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:53:14] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:53:21,170 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:21,174 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:22,154 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:22,748 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:22,750 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:31,646 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:31,647 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:31,648 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:31,909 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:32,452 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:32,455 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:42,434 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:42,437 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:42,614 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:43,197 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:43,199 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:53:52,253 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:52,257 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:52,258 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:53:52,362 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:53:52,945 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:53:52,947 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:03,440 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:03,443 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:03,444 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:04,108 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:04,670 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:04,673 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:13,391 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:13,392 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:13,393 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:13,832 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:14,391 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:14,395 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:25,578 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:25,582 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:25,583 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:26,575 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:27,232 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:27,236 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:35,488 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:35,492 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:36,434 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:37,319 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:37,324 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:46,301 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:46,302 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:46,506 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:47,184 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:47,189 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:54:57,229 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:57,232 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:57,233 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:54:57,370 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:54:57,957 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:54:57,960 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:06,508 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:06,511 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:06,511 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:07,134 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:07,749 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:07,751 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:17,357 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:17,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:17,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:17,924 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:18,526 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:18,529 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:40,946 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:40,947 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:40,947 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:41,810 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:42,366 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:42,370 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:55:51,151 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:51,155 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\quickbooks_client.py', reloading
2025-06-15 09:55:51,525 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:55:52,070 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:55:52,073 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:56:08,990 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-15 09:56:08,990 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 09:56:09,002 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:56:09,683 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:56:09,686 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:56:32,178 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:32] "GET / HTTP/1.1" 200 -
2025-06-15 09:56:32,278 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:32] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:56:34,189 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:56:34,976 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:56:34,976 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,977 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:56:34,977 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,978 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:56:34,978 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:56:34,978 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:56:34,979 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:56:34,979 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,980 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,980 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:56:34,980 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,980 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=LxQDOUYzStkkQJtTd82XzuAnARju3X
2025-06-15 09:56:34,981 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:56:34,981 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:34,983 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:34] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:56:41,941 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,941 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:56:41,942 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,942 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:56:41,942 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:56:41,942 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:56:41,942 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:56:41,942 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:56:41,943 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=LxQDOUYzStkkQJtTd82XzuAnARju3X
2025-06-15 09:56:41,943 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:56:41,943 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:56:41,943 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:56:41] "[32mGET /connect HTTP/1.1[0m" 302 -
2025-06-15 09:57:13,684 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:13] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:57:13,688 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:13] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:18,790 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:18] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:19,094 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:19] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:57:22,322 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:22] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:57:22,418 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:22] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:22,551 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:22] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:57:33,285 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:33] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:57:33,595 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:33] "GET / HTTP/1.1" 200 -
2025-06-15 09:57:33,948 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:57:33] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:57:46,577 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:57:46,580 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:57:46,581 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Personal\\gitcode\\python\\Versions\\40QB\\app.py', reloading
2025-06-15 09:57:46,701 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-15 09:57:47,294 - werkzeug - WARNING -  * Debugger is active!
2025-06-15 09:57:47,297 - werkzeug - INFO -  * Debugger PIN: 209-918-523
2025-06-15 09:58:28,243 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:58:28,844 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:58:28,845 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,845 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:58:28,845 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,845 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:58:28,845 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:58:28,845 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:58:28,846 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,846 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,846 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=j5SaIDzIv7FVe3fh45h2FOgWaTXHmL
2025-06-15 09:58:28,846 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:58:28,846 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:28,848 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:28] "[32mGET /test_logging HTTP/1.1[0m" 302 -
2025-06-15 09:58:28,887 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:28] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:58:28,892 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:28] "GET / HTTP/1.1" 200 -
2025-06-15 09:58:45,014 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): developer.intuit.com:443
2025-06-15 09:58:45,711 - urllib3.connectionpool - DEBUG - https://developer.intuit.com:443 "GET /.well-known/openid_sandbox_configuration/ HTTP/1.1" 200 1006
2025-06-15 09:58:45,712 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,712 - quickbooks_client - INFO - OAUTH STEP: GET_AUTHORIZATION_URL
2025-06-15 09:58:45,712 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,712 - quickbooks_client - INFO - client_id: ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd
2025-06-15 09:58:45,713 - quickbooks_client - INFO - redirect_uri: http://localhost:5000/callback
2025-06-15 09:58:45,713 - quickbooks_client - INFO - scopes: ['com.intuit.quickbooks.accounting']
2025-06-15 09:58:45,713 - quickbooks_client - INFO - environment: sandbox
2025-06-15 09:58:45,713 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,713 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,714 - quickbooks_client - INFO - OAUTH STEP: AUTHORIZATION_URL_GENERATED
2025-06-15 09:58:45,714 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,714 - quickbooks_client - INFO - auth_url: https://appcenter.intuit.com/connect/oauth2?client_id=ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Fcallback&state=2ricfWv31m6WyFakyWJIcI9Fevidm8
2025-06-15 09:58:45,714 - quickbooks_client - INFO - url_length: 253
2025-06-15 09:58:45,714 - quickbooks_client - INFO - [OAUTH] ======================================================================
2025-06-15 09:58:45,716 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:45] "[32mGET /test_logging HTTP/1.1[0m" 302 -
2025-06-15 09:58:45,736 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:45] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:58:45,741 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:45] "GET / HTTP/1.1" 200 -
2025-06-15 09:58:46,192 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:46] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:58:55,630 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:55] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:58:55,931 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:55] "GET / HTTP/1.1" 200 -
2025-06-15 09:58:56,275 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:58:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:59:30,971 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:30] "GET / HTTP/1.1" 200 -
2025-06-15 09:59:31,276 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:31] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-06-15 09:59:33,882 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:33] "[32mGET /logs HTTP/1.1[0m" 302 -
2025-06-15 09:59:34,094 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:34] "GET / HTTP/1.1" 200 -
2025-06-15 09:59:34,133 - werkzeug - INFO - 127.0.0.1 - - [15/Jun/2025 09:59:34] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
