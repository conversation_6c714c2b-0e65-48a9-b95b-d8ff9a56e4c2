{% extends "base.html" %}

{% block title %}API Logs - QuickBooks Account Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>📊 QuickBooks API Logs</h2>
            <div>
                <button class="btn btn-outline-primary me-2" onclick="refreshLogs()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button class="btn btn-outline-secondary me-2" onclick="toggleAutoRefresh()">
                    <i class="fas fa-play"></i> <span id="autoRefreshText">Auto Refresh</span>
                </button>
                <a href="{{ url_for('test_logging') }}" class="btn btn-outline-success me-2">
                    <i class="fas fa-vial"></i> Test Logging
                </a>
                <a href="{{ url_for('clear_logs') }}" class="btn btn-outline-danger"
                   onclick="return confirm('Are you sure you want to clear all logs?')">
                    <i class="fas fa-trash"></i> Clear Logs
                </a>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt"></i> 
                            Detailed API Logs 
                            <span class="badge bg-primary" id="logCount">{{ log_count }}</span>
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchLogs" placeholder="Search logs...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchLogs()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="logContainer">
                    {% if logs %}
                    <pre class="log-content" id="logContent">{% for log in logs %}{{ log }}
{% endfor %}</pre>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Logs Available</h4>
                        <p class="text-muted">Start using the application to see detailed API logs here.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Total Log Entries</h5>
                        <h2 class="text-primary" id="totalLogs">{{ log_count }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Last Updated</h5>
                        <h6 class="text-info" id="lastUpdated">{{ moment().format('YYYY-MM-DD HH:mm:ss') }}</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Auto Refresh</h5>
                        <h6 class="text-success" id="autoRefreshStatus">Disabled</h6>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Log Legend</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Log Levels</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">INFO</span> - General information</li>
                            <li><span class="badge bg-warning">WARNING</span> - Warning messages</li>
                            <li><span class="badge bg-danger">ERROR</span> - Error messages</li>
                            <li><span class="badge bg-secondary">DEBUG</span> - Debug information</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Log Sections</h6>
                        <ul class="list-unstyled">
                            <li><strong>[OAUTH]</strong> - OAuth authentication flow with complete URLs and parameters</li>
                            <li><strong>[ACCOUNT]</strong> - Account creation operations with request/response details</li>
                            <li><strong>[ACCOUNTS]</strong> - Account retrieval operations with filtering and results</li>
                            <li><strong>>>> OUTGOING REQUEST</strong> - Complete HTTP requests with headers and body</li>
                            <li><strong><<< INCOMING RESPONSE</strong> - Complete HTTP responses with status and data</li>
                        </ul>

                        <h6>What You'll See in Logs</h6>
                        <ul class="list-unstyled small">
                            <li>✅ <strong>Complete OAuth URLs</strong> with all query parameters</li>
                            <li>✅ <strong>Request/Response Headers</strong> (sensitive data masked)</li>
                            <li>✅ <strong>JSON Request Bodies</strong> formatted for readability</li>
                            <li>✅ <strong>HTTP Status Codes</strong> and response times</li>
                            <li>✅ <strong>Error Details</strong> with full stack traces</li>
                            <li>✅ <strong>API Endpoint URLs</strong> and HTTP methods</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

function refreshLogs() {
    fetch('/api/logs')
        .then(response => response.json())
        .then(data => {
            if (data.logs) {
                document.getElementById('logContent').textContent = data.logs.join('\n');
                document.getElementById('logCount').textContent = data.count;
                document.getElementById('totalLogs').textContent = data.count;
                document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
                
                // Scroll to bottom
                const logContainer = document.getElementById('logContainer');
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        })
        .catch(error => {
            console.error('Error refreshing logs:', error);
        });
}

function toggleAutoRefresh() {
    if (isAutoRefreshEnabled) {
        // Disable auto refresh
        clearInterval(autoRefreshInterval);
        isAutoRefreshEnabled = false;
        document.getElementById('autoRefreshText').textContent = 'Auto Refresh';
        document.getElementById('autoRefreshStatus').textContent = 'Disabled';
        document.getElementById('autoRefreshStatus').className = 'text-danger';
    } else {
        // Enable auto refresh
        autoRefreshInterval = setInterval(refreshLogs, 5000); // Refresh every 5 seconds
        isAutoRefreshEnabled = true;
        document.getElementById('autoRefreshText').textContent = 'Stop Auto Refresh';
        document.getElementById('autoRefreshStatus').textContent = 'Enabled (5s)';
        document.getElementById('autoRefreshStatus').className = 'text-success';
    }
}

function searchLogs() {
    const searchTerm = document.getElementById('searchLogs').value.toLowerCase();
    const logContent = document.getElementById('logContent');
    const originalText = logContent.textContent;
    
    if (searchTerm === '') {
        // Reset to original content
        logContent.innerHTML = originalText;
        return;
    }
    
    // Highlight search terms
    const lines = originalText.split('\n');
    const filteredLines = lines.filter(line => 
        line.toLowerCase().includes(searchTerm)
    );
    
    if (filteredLines.length > 0) {
        const highlightedText = filteredLines.map(line => {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return line.replace(regex, '<mark>$1</mark>');
        }).join('\n');
        
        logContent.innerHTML = highlightedText;
    } else {
        logContent.innerHTML = 'No matching log entries found.';
    }
}

// Search on Enter key
document.getElementById('searchLogs').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchLogs();
    }
});

// Auto-scroll to bottom on page load
document.addEventListener('DOMContentLoaded', function() {
    const logContainer = document.getElementById('logContainer');
    if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // Update last updated time
    document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
});
</script>

<style>
.log-container {
    max-height: 600px;
    overflow-y: auto;
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    border-radius: 0 0 0.375rem 0.375rem;
}

.log-content {
    margin: 0;
    padding: 15px;
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: transparent;
    border: none;
    color: inherit;
}

.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #2d2d2d;
}

.log-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}

mark {
    background-color: #ffeb3b;
    color: #000;
    padding: 2px 4px;
    border-radius: 2px;
}
</style>
{% endblock %}
