{% extends "base.html" %}

{% block title %}Home - QuickBooks Account Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">QuickBooks Account Manager</h1>
            <p class="lead">Manage your QuickBooks accounts with ease</p>
        </div>

        {% if not authenticated %}
        <div class="card shadow-sm">
            <div class="card-body text-center">
                <h3 class="card-title">Get Started</h3>
                <p class="card-text">Connect to your QuickBooks Online account to start managing accounts.</p>
                <a href="{{ url_for('connect_to_quickbooks') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-link"></i> Connect to QuickBooks
                </a>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">Create Accounts</h5>
                        <p class="card-text">Add new accounts to your QuickBooks company with various account types and subtypes.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">View Accounts</h5>
                        <p class="card-text">Browse and search through all your existing QuickBooks accounts.</p>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body text-center">
                        <h4 class="card-title text-success">Create New Account</h4>
                        <p class="card-text">Add a new account to your QuickBooks company.</p>
                        <a href="{{ url_for('create_account') }}" class="btn btn-success btn-lg">
                            <i class="fas fa-plus"></i> Create Account
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body text-center">
                        <h4 class="card-title text-info">View Accounts</h4>
                        <p class="card-text">Browse all your existing accounts.</p>
                        <a href="{{ url_for('view_accounts') }}" class="btn btn-info btn-lg">
                            <i class="fas fa-list"></i> View Accounts
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Connection Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <span class="badge bg-success me-2">Connected</span>
                    <span>Successfully connected to QuickBooks Online</span>
                </div>
                <small class="text-muted">Company ID: {{ session.company_id }}</small>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">About This Application</h5>
            </div>
            <div class="card-body">
                <p>This application demonstrates integration with the QuickBooks Online API using Python. It provides:</p>
                <ul>
                    <li><strong>OAuth 2.0 Authentication:</strong> Secure connection to QuickBooks Online</li>
                    <li><strong>Account Creation:</strong> Create new accounts with various types and subtypes</li>
                    <li><strong>Account Reading:</strong> View and browse existing accounts</li>
                    <li><strong>RESTful API:</strong> JSON endpoints for programmatic access</li>
                </ul>
                <p class="mb-0"><strong>Technologies Used:</strong> Python, Flask, QuickBooks Online API, Bootstrap</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
{% endblock %}
