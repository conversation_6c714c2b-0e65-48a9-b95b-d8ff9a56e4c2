{% extends "base.html" %}

{% block title %}Create Account - QuickBooks Account Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header">
                <h3 class="mb-0">Create New Account</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Account Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="form-text">Enter a unique name for the account</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="account_number" class="form-label">Account Number</label>
                            <input type="text" class="form-control" id="account_number" name="account_number">
                            <div class="form-text">Optional account number</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="account_type" class="form-label">Account Type *</label>
                            <select class="form-select" id="account_type" name="account_type" required>
                                <option value="">Select Account Type</option>
                                <option value="Asset">Asset</option>
                                <option value="Liability">Liability</option>
                                <option value="Equity">Equity</option>
                                <option value="Income">Income</option>
                                <option value="Expense">Expense</option>
                                <option value="Other Income">Other Income</option>
                                <option value="Other Expense">Other Expense</option>
                                <option value="Cost of Goods Sold">Cost of Goods Sold</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="account_sub_type" class="form-label">Account Sub Type</label>
                            <select class="form-select" id="account_sub_type" name="account_sub_type">
                                <option value="">Select Sub Type</option>
                                <!-- Asset Sub Types -->
                                <optgroup label="Asset Sub Types" id="asset_subtypes" style="display:none;">
                                    <option value="AccountsReceivable">Accounts Receivable</option>
                                    <option value="Bank">Bank</option>
                                    <option value="Cash">Cash</option>
                                    <option value="FixedAsset">Fixed Asset</option>
                                    <option value="Inventory">Inventory</option>
                                    <option value="OtherCurrentAsset">Other Current Asset</option>
                                    <option value="OtherAsset">Other Asset</option>
                                </optgroup>
                                <!-- Liability Sub Types -->
                                <optgroup label="Liability Sub Types" id="liability_subtypes" style="display:none;">
                                    <option value="AccountsPayable">Accounts Payable</option>
                                    <option value="CreditCard">Credit Card</option>
                                    <option value="LongTermLiability">Long Term Liability</option>
                                    <option value="OtherCurrentLiability">Other Current Liability</option>
                                </optgroup>
                                <!-- Equity Sub Types -->
                                <optgroup label="Equity Sub Types" id="equity_subtypes" style="display:none;">
                                    <option value="Equity">Equity</option>
                                    <option value="RetainedEarnings">Retained Earnings</option>
                                </optgroup>
                                <!-- Income Sub Types -->
                                <optgroup label="Income Sub Types" id="income_subtypes" style="display:none;">
                                    <option value="Income">Income</option>
                                    <option value="SalesOfProductIncome">Sales of Product Income</option>
                                    <option value="ServiceFeeIncome">Service/Fee Income</option>
                                </optgroup>
                                <!-- Expense Sub Types -->
                                <optgroup label="Expense Sub Types" id="expense_subtypes" style="display:none;">
                                    <option value="Expense">Expense</option>
                                    <option value="AdvertisingPromotional">Advertising/Promotional</option>
                                    <option value="BadDebts">Bad Debts</option>
                                    <option value="BankCharges">Bank Charges</option>
                                    <option value="CharitableContributions">Charitable Contributions</option>
                                </optgroup>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        <div class="form-text">Optional description for the account</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Account</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Account Type Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Asset Accounts</h6>
                        <p class="small">Track what your company owns (cash, inventory, equipment)</p>
                        
                        <h6>Liability Accounts</h6>
                        <p class="small">Track what your company owes (loans, credit cards, accounts payable)</p>
                        
                        <h6>Equity Accounts</h6>
                        <p class="small">Track owner's equity and retained earnings</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Income Accounts</h6>
                        <p class="small">Track revenue from sales and services</p>
                        
                        <h6>Expense Accounts</h6>
                        <p class="small">Track business expenses and costs</p>
                        
                        <h6>Other Income/Expense</h6>
                        <p class="small">Track non-operating income and expenses</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('account_type').addEventListener('change', function() {
    const accountType = this.value;
    const subTypeSelect = document.getElementById('account_sub_type');
    const optgroups = subTypeSelect.querySelectorAll('optgroup');
    
    // Hide all optgroups
    optgroups.forEach(group => {
        group.style.display = 'none';
    });
    
    // Reset sub type selection
    subTypeSelect.value = '';
    
    // Show relevant optgroup based on account type
    if (accountType === 'Asset') {
        document.getElementById('asset_subtypes').style.display = 'block';
    } else if (accountType === 'Liability') {
        document.getElementById('liability_subtypes').style.display = 'block';
    } else if (accountType === 'Equity') {
        document.getElementById('equity_subtypes').style.display = 'block';
    } else if (accountType === 'Income') {
        document.getElementById('income_subtypes').style.display = 'block';
    } else if (accountType === 'Expense') {
        document.getElementById('expense_subtypes').style.display = 'block';
    }
});
</script>
{% endblock %}
