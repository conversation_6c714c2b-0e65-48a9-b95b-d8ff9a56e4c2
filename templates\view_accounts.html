{% extends "base.html" %}

{% block title %}View Accounts - QuickB<PERSON>s Account Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>QuickBooks Accounts</h2>
            <a href="{{ url_for('create_account') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create New Account
            </a>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">Account List ({{ accounts|length }} accounts)</h5>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search accounts...">
                            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if accounts %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="accountsTable">
                        <thead class="table-light">
                            <tr>
                                <th>Account Name</th>
                                <th>Type</th>
                                <th>Sub Type</th>
                                <th>Account Number</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts %}
                            <tr>
                                <td>
                                    <strong>{{ account.Name }}</strong>
                                    {% if account.Description %}
                                    <br><small class="text-muted">{{ account.Description }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ account.AccountType }}</span>
                                </td>
                                <td>
                                    {% if account.AccountSubType %}
                                    <span class="badge bg-light text-dark">{{ account.AccountSubType }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.AcctNum %}
                                    {{ account.AcctNum }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.CurrentBalance %}
                                    ${{ "%.2f"|format(account.CurrentBalance) }}
                                    {% else %}
                                    $0.00
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.Active %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('view_account_detail', account_id=account.Id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Accounts Found</h4>
                    <p class="text-muted">Create your first account to get started.</p>
                    <a href="{{ url_for('create_account') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Account
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        {% if accounts %}
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Total Accounts</h5>
                        <h2 class="text-primary">{{ accounts|length }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Active</h5>
                        <h2 class="text-success">{{ accounts|selectattr("Active")|list|length }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Asset Accounts</h5>
                        <h2 class="text-info">{{ accounts|selectattr("AccountType", "equalto", "Asset")|list|length }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Expense Accounts</h5>
                        <h2 class="text-warning">{{ accounts|selectattr("AccountType", "equalto", "Expense")|list|length }}</h2>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Search functionality
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#accountsTable tbody tr');
    
    tableRows.forEach(row => {
        const accountName = row.cells[0].textContent.toLowerCase();
        const accountType = row.cells[1].textContent.toLowerCase();
        const accountSubType = row.cells[2].textContent.toLowerCase();
        
        if (accountName.includes(searchTerm) || 
            accountType.includes(searchTerm) || 
            accountSubType.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Search button click
document.getElementById('searchBtn').addEventListener('click', function() {
    document.getElementById('searchInput').focus();
});

// Enter key search
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
    }
});
</script>
{% endblock %}
