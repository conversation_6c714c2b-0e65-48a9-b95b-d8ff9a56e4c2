{% extends "base.html" %}

{% block title %}Account Details - QuickBooks Account Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Account Details</h2>
            <a href="{{ url_for('view_accounts') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Accounts
            </a>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ account.Name }}</h4>
                    {% if account.Active %}
                    <span class="badge bg-success">Active</span>
                    {% else %}
                    <span class="badge bg-danger">Inactive</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Account ID:</th>
                                <td>{{ account.Id }}</td>
                            </tr>
                            <tr>
                                <th>Account Name:</th>
                                <td><strong>{{ account.Name }}</strong></td>
                            </tr>
                            <tr>
                                <th>Account Type:</th>
                                <td>
                                    <span class="badge bg-primary">{{ account.AccountType }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Account Sub Type:</th>
                                <td>
                                    {% if account.AccountSubType %}
                                    <span class="badge bg-secondary">{{ account.AccountSubType }}</span>
                                    {% else %}
                                    <span class="text-muted">Not specified</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Account Number:</th>
                                <td>
                                    {% if account.AcctNum %}
                                    {{ account.AcctNum }}
                                    {% else %}
                                    <span class="text-muted">Not specified</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Current Balance:</th>
                                <td>
                                    <h4 class="mb-0">
                                        {% if account.CurrentBalance %}
                                        ${{ "%.2f"|format(account.CurrentBalance) }}
                                        {% else %}
                                        $0.00
                                        {% endif %}
                                    </h4>
                                </td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    {% if account.Active %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Created Date:</th>
                                <td>
                                    {% if account.MetaData and account.MetaData.CreateTime %}
                                    {{ account.MetaData.CreateTime.strftime('%Y-%m-%d %H:%M:%S') }}
                                    {% else %}
                                    <span class="text-muted">Not available</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Last Updated:</th>
                                <td>
                                    {% if account.MetaData and account.MetaData.LastUpdatedTime %}
                                    {{ account.MetaData.LastUpdatedTime.strftime('%Y-%m-%d %H:%M:%S') }}
                                    {% else %}
                                    <span class="text-muted">Not available</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if account.Description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ account.Description }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Account Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Account Classification</h6>
                        <p class="small">
                            This account is classified as a <strong>{{ account.AccountType }}</strong> account.
                            {% if account.AccountSubType %}
                            More specifically, it's a <strong>{{ account.AccountSubType }}</strong> type.
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Usage Guidelines</h6>
                        <p class="small">
                            {% if account.AccountType == 'Asset' %}
                            Use this account to track assets owned by your company.
                            {% elif account.AccountType == 'Liability' %}
                            Use this account to track amounts owed by your company.
                            {% elif account.AccountType == 'Equity' %}
                            Use this account to track owner's equity and retained earnings.
                            {% elif account.AccountType == 'Income' %}
                            Use this account to track revenue and income.
                            {% elif account.AccountType == 'Expense' %}
                            Use this account to track business expenses.
                            {% else %}
                            Use this account according to your accounting practices.
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="{{ url_for('view_accounts') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-list"></i> Back to All Accounts
                </a>
                <a href="{{ url_for('create_account') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Another Account
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
