from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from quickbooks_client import <PERSON><PERSON>ooksClient
from config import Config
import logging
import os
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(Config)

# Initialize QuickBooks client
qb_client = QuickBooksClient()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html', 
                         authenticated=session.get('authenticated', False))

@app.route('/connect')
def connect_to_quickbooks():
    """Initiate QuickBooks OAuth flow"""
    try:
        auth_url = qb_client.get_authorization_url()
        return redirect(auth_url)
    except Exception as e:
        flash(f'Error connecting to QuickBooks: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/callback')
def oauth_callback():
    """Handle OAuth callback from QuickB<PERSON>s"""
    try:
        auth_code = request.args.get('code')
        realm_id = request.args.get('realmId')
        
        if not auth_code or not realm_id:
            flash('Authorization failed: Missing parameters', 'error')
            return redirect(url_for('index'))
        
        # Exchange code for tokens
        success = qb_client.get_bearer_token(auth_code, realm_id)
        
        if success:
            # Store session data
            session['authenticated'] = True
            session['company_id'] = realm_id
            session['access_token'] = qb_client.auth_client.access_token
            session['refresh_token'] = qb_client.refresh_token
            
            flash('Successfully connected to QuickBooks!', 'success')
        else:
            flash('Failed to authenticate with QuickBooks', 'error')
            
    except Exception as e:
        logger.error(f"OAuth callback error: {str(e)}")
        flash(f'Authentication error: {str(e)}', 'error')
    
    return redirect(url_for('index'))

@app.route('/disconnect')
def disconnect():
    """Disconnect from QuickBooks"""
    session.clear()
    flash('Disconnected from QuickBooks', 'info')
    return redirect(url_for('index'))

@app.route('/create_account', methods=['GET', 'POST'])
def create_account():
    """Create a new account"""
    if not session.get('authenticated'):
        flash('Please connect to QuickBooks first', 'warning')
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        try:
            # Setup client with session tokens
            qb_client.setup_client_with_tokens(
                session['access_token'],
                session['refresh_token'],
                session['company_id']
            )
            
            # Prepare account data
            account_data = {
                'name': request.form.get('name'),
                'account_type': request.form.get('account_type'),
                'account_sub_type': request.form.get('account_sub_type'),
                'description': request.form.get('description'),
                'account_number': request.form.get('account_number')
            }
            
            # Validate required fields
            if not account_data['name'] or not account_data['account_type']:
                flash('Account name and type are required', 'error')
                return render_template('create_account.html')
            
            # Create account
            account = qb_client.create_account(account_data)
            flash(f'Account "{account.Name}" created successfully!', 'success')
            return redirect(url_for('view_accounts'))
            
        except Exception as e:
            logger.error(f"Error creating account: {str(e)}")
            flash(f'Error creating account: {str(e)}', 'error')
    
    return render_template('create_account.html')

@app.route('/view_accounts')
def view_accounts():
    """View all accounts"""
    if not session.get('authenticated'):
        flash('Please connect to QuickBooks first', 'warning')
        return redirect(url_for('index'))
    
    try:
        # Setup client with session tokens
        qb_client.setup_client_with_tokens(
            session['access_token'],
            session['refresh_token'],
            session['company_id']
        )
        
        # Get accounts
        accounts = qb_client.get_accounts()
        return render_template('view_accounts.html', accounts=accounts)
        
    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        flash(f'Error retrieving accounts: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/account/<int:account_id>')
def view_account_detail(account_id):
    """View account details"""
    if not session.get('authenticated'):
        flash('Please connect to QuickBooks first', 'warning')
        return redirect(url_for('index'))
    
    try:
        # Setup client with session tokens
        qb_client.setup_client_with_tokens(
            session['access_token'],
            session['refresh_token'],
            session['company_id']
        )
        
        # Get specific account
        account = qb_client.get_account_by_id(account_id)
        return render_template('account_detail.html', account=account)
        
    except Exception as e:
        logger.error(f"Error getting account details: {str(e)}")
        flash(f'Error retrieving account details: {str(e)}', 'error')
        return redirect(url_for('view_accounts'))

@app.route('/api/accounts')
def api_get_accounts():
    """API endpoint to get accounts as JSON"""
    if not session.get('authenticated'):
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        # Setup client with session tokens
        qb_client.setup_client_with_tokens(
            session['access_token'],
            session['refresh_token'],
            session['company_id']
        )

        # Get accounts
        accounts = qb_client.get_accounts()

        # Convert to JSON-serializable format
        accounts_data = []
        for account in accounts:
            accounts_data.append({
                'Id': account.Id,
                'Name': account.Name,
                'AccountType': account.AccountType,
                'AccountSubType': getattr(account, 'AccountSubType', ''),
                'CurrentBalance': getattr(account, 'CurrentBalance', 0),
                'Active': getattr(account, 'Active', True),
                'Description': getattr(account, 'Description', ''),
                'AcctNum': getattr(account, 'AcctNum', '')
            })

        return jsonify({'accounts': accounts_data})

    except Exception as e:
        logger.error(f"API error getting accounts: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/logs')
def view_logs():
    """View detailed API logs"""
    try:
        log_file_path = 'quickbooks_api.log'
        logs = []

        if os.path.exists(log_file_path):
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()
                # Get last 500 lines to avoid memory issues
                recent_lines = log_lines[-500:] if len(log_lines) > 500 else log_lines
                logs = [line.strip() for line in recent_lines if line.strip()]

        return render_template('logs.html', logs=logs, log_count=len(logs))

    except Exception as e:
        flash(f'Error reading logs: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/api/logs')
def api_get_logs():
    """API endpoint to get logs as JSON"""
    try:
        log_file_path = 'quickbooks_api.log'
        logs = []

        if os.path.exists(log_file_path):
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()
                # Get last 200 lines for API
                recent_lines = log_lines[-200:] if len(log_lines) > 200 else log_lines
                logs = [line.strip() for line in recent_lines if line.strip()]

        return jsonify({
            'logs': logs,
            'count': len(logs),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/clear_logs')
def clear_logs():
    """Clear the log file"""
    try:
        log_file_path = 'quickbooks_api.log'
        if os.path.exists(log_file_path):
            with open(log_file_path, 'w') as f:
                f.write('')
            flash('Logs cleared successfully', 'success')
        else:
            flash('No log file found', 'info')
    except Exception as e:
        flash(f'Error clearing logs: {str(e)}', 'error')

    return redirect(url_for('view_logs'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
