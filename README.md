# QuickBooks Account Manager

A Python Flask web application that integrates with QuickBooks Online API to provide account creation and reading functionality.

## Features

- **OAuth 2.0 Authentication**: Secure connection to QuickBooks Online
- **Account Creation**: Create new accounts with various types and subtypes
- **Account Reading**: View and browse existing accounts
- **Web Interface**: User-friendly web interface built with Bootstrap
- **RESTful API**: JSON endpoints for programmatic access
- **Search Functionality**: Search and filter accounts
- **Responsive Design**: Works on desktop and mobile devices

## Prerequisites

- Python 3.7 or higher
- QuickBooks Online Developer Account
- QuickBooks Online Sandbox Company (for testing)

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the application**:
   - The QuickBooks credentials are already configured in `config.py`
   - Client ID: `ABNOZkFilhdSFUrmsokIj1RVh6GXa8OiigdYPIy94Cmk1BDaHd`
   - Client Secret: `0HyzRWUl1pa8hnS5GcpuCWs0M0OpcLDrmw9hQX4u`
   - Environment: `sandbox`

4. **Run the application**:
   ```bash
   python app.py
   ```

5. **Access the application**:
   - Open your web browser and go to `http://localhost:5000`

## Usage

### 1. Connect to QuickBooks

1. Click "Connect to QuickBooks" on the home page
2. You'll be redirected to Intuit's OAuth page
3. Sign in with your QuickBooks Online account
4. Authorize the application to access your QuickBooks data
5. You'll be redirected back to the application

### 2. Create an Account

1. After connecting, click "Create Account" or navigate to the create account page
2. Fill in the account details:
   - **Account Name** (required): Unique name for the account
   - **Account Type** (required): Select from Asset, Liability, Equity, Income, Expense, etc.
   - **Account Sub Type**: More specific classification (auto-populated based on type)
   - **Account Number**: Optional account number
   - **Description**: Optional description
3. Click "Create Account" to save

### 3. View Accounts

1. Click "View Accounts" to see all accounts
2. Use the search box to filter accounts by name, type, or subtype
3. Click "View" next to any account to see detailed information
4. View account statistics and summaries

## API Endpoints

The application also provides RESTful API endpoints:

- `GET /api/accounts` - Get all accounts as JSON

Example response:
```json
{
  "accounts": [
    {
      "Id": "1",
      "Name": "Checking Account",
      "AccountType": "Asset",
      "AccountSubType": "Bank",
      "CurrentBalance": 1000.00,
      "Active": true,
      "Description": "Main business checking account",
      "AcctNum": "1001"
    }
  ]
}
```

## Account Types and Subtypes

### Asset Accounts
- **Bank**: Business checking, savings accounts
- **Cash**: Petty cash, cash on hand
- **Accounts Receivable**: Money owed by customers
- **Inventory**: Products for sale
- **Fixed Asset**: Equipment, buildings, vehicles
- **Other Current Asset**: Short-term assets
- **Other Asset**: Long-term assets

### Liability Accounts
- **Accounts Payable**: Money owed to vendors
- **Credit Card**: Business credit card balances
- **Long Term Liability**: Loans, mortgages
- **Other Current Liability**: Short-term debts

### Equity Accounts
- **Equity**: Owner's equity
- **Retained Earnings**: Accumulated profits

### Income Accounts
- **Income**: General income
- **Sales of Product Income**: Product sales revenue
- **Service/Fee Income**: Service revenue

### Expense Accounts
- **Expense**: General expenses
- **Advertising/Promotional**: Marketing costs
- **Bank Charges**: Banking fees
- **Bad Debts**: Uncollectible accounts

## File Structure

```
quickbooks-account-manager/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── quickbooks_client.py  # QuickBooks API client wrapper
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── create_account.html
│   ├── view_accounts.html
│   └── account_detail.html
└── static/              # Static files
    └── style.css        # Custom CSS styles
```

## Technologies Used

- **Backend**: Python, Flask
- **QuickBooks Integration**: python-quickbooks, intuit-oauth
- **Frontend**: HTML5, Bootstrap 5, JavaScript
- **Authentication**: OAuth 2.0

## Error Handling

The application includes comprehensive error handling:
- OAuth authentication errors
- QuickBooks API errors
- Form validation errors
- Network connectivity issues

All errors are displayed to the user with helpful messages.

## Security

- Uses OAuth 2.0 for secure authentication
- Session-based token management
- HTTPS recommended for production use
- Input validation and sanitization

## Development

To extend the application:

1. **Add new account operations**: Extend `quickbooks_client.py`
2. **Add new routes**: Add routes to `app.py`
3. **Create new templates**: Add HTML templates in `templates/`
4. **Modify styling**: Update `static/style.css`

## Troubleshooting

### Common Issues

1. **"Not authenticated" errors**: Make sure you've connected to QuickBooks first
2. **OAuth callback errors**: Check that the redirect URI matches the configuration
3. **Account creation errors**: Verify required fields are filled and account name is unique
4. **API errors**: Check QuickBooks API status and your sandbox company

### Logs

The application logs important events and errors. Check the console output for debugging information.

## Support

For issues related to:
- **QuickBooks API**: Check [Intuit Developer Documentation](https://developer.intuit.com/)
- **OAuth**: Review [OAuth 2.0 Guide](https://developer.intuit.com/app/developer/qbo/docs/develop/authentication-and-authorization/oauth-2.0)
- **Python libraries**: Check respective library documentation

## License

This project is for educational and demonstration purposes.
